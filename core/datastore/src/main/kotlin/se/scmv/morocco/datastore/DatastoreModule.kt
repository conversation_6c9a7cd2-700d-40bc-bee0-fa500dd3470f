package se.scmv.morocco.datastore

import android.content.Context
import androidx.datastore.core.DataStore
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import se.scmv.morocco.datastore.prefs.AvitoPreferencesDataSource
import se.scmv.morocco.datastore.proto.getAccountDataStore
import javax.inject.Singleton
import se.scmv.morocco.datastore.PbAccount

@Module
@InstallIn(SingletonComponent::class)
object DatastoreModule {

    @Provides
    @Singleton
    fun provideAvitoPreferencesDataSource(
        @ApplicationContext context: Context
    ): AvitoPreferencesDataSource {
        return AvitoPreferencesDataSource(context)
    }

    @Provides
    @Singleton
    fun provideAccountDataSource(
        @ApplicationContext context: Context
    ): DataStore<PbAccount> {
        return getAccountDataStore(context)
    }
}