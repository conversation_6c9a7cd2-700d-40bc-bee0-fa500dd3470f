plugins {
    id("avito.android.library")
    id("avito.android.hilt")
    alias(libs.plugins.wire)
}

android {
    namespace = "se.scmv.morocco.datastore"
}

dependencies {
    implementation(project(":core:domain"))

    // Datastore
    api(libs.androidx.datastore)
    implementation(libs.androidx.datastore.preferences)
}

wire {
    kotlin {}
    sourcePath {
        srcDir("src/main/proto")
    }
}

androidComponents.beforeVariants {
    android.sourceSets.getByName(it.name) {
        val buildDir = layout.buildDirectory.get().asFile
        java.srcDir(buildDir.resolve("build/generated/source/wire/${it.name}/java"))
        kotlin.srcDir(buildDir.resolve("build/generated/source/wire/${it.name}/kotlin"))
    }
}

// Ensure Wire generates sources before KSP
tasks.withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile>().configureEach {
    dependsOn("generateProtos")
}

// If using KSP, make sure it runs after Wire
tasks.matching { it.name.contains("ksp") }.configureEach {
    dependsOn("generateProtos")
}