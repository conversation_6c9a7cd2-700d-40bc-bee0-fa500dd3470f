package se.scmv.morocco.authentication.presentation.private_account.auth

import androidx.activity.ComponentActivity
import androidx.compose.ui.test.junit4.createAndroidComposeRule
import org.junit.Rule
import org.junit.Test
import se.scmv.morocco.authentication.data.repository.AuthenticationRepositoryConfigurableImpl
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.models.NetworkAndBackendErrors
import se.scmv.morocco.domain.models.NetworkErrors

class PrivateAccountSignInEndTest {

    @get:Rule(order = 0)
    val composeTestRule = createAndroidComposeRule<ComponentActivity>()

    @Test
    fun signIn_email_password_backendError() {
        launchPrivateAccountSignInScreen(
            rule = composeTestRule,
            authenticationRepository = AuthenticationRepositoryConfigurableImpl(
                signInResponse = Resource.Failure(
                    NetworkAndBackendErrors.Backend(AuthenticationRepositoryConfigurableImpl.SIGN_IN_ERROR)
                )
            )
        ) {
            typeEmailOrPhone("<EMAIL>")
            typePassword("Azerty@123")
            submit()
        } verify {
            emailHasNoError()
            passwordHasNoError()
            backendErrorIsDisplayed()
        }
    }

    @Test
    fun signIn_phone_password_backendError() {
        launchPrivateAccountSignInScreen(
            rule = composeTestRule,
            authenticationRepository = AuthenticationRepositoryConfigurableImpl(
                signInResponse = Resource.Failure(
                    NetworkAndBackendErrors.Backend(AuthenticationRepositoryConfigurableImpl.SIGN_IN_ERROR)
                )
            )
        ) {
            typeEmailOrPhone("**********")
            typePassword("Azerty@123")
            submit()
        } verify {
            emailHasNoError()
            passwordHasNoError()
            backendErrorIsDisplayed()
        }
    }

    @Test
    fun signIn_email_password_networkError() {
        launchPrivateAccountSignInScreen(
            rule = composeTestRule,
            authenticationRepository = AuthenticationRepositoryConfigurableImpl(
                signInResponse = Resource.Failure(NetworkAndBackendErrors.Network(NetworkErrors.NO_INTERNET))
            )
        ) {
            typeEmailOrPhone("<EMAIL>")
            typePassword("Azerty@123")
            submit()
        } verify {
            emailHasNoError()
            passwordHasNoError()
            networkErrorIsDisplayed()
        }
    }

    @Test
    fun signIn_email_password_success() {
        launchPrivateAccountSignInScreen(
            rule = composeTestRule,
            authenticationRepository = AuthenticationRepositoryConfigurableImpl()
        ) {
            typeEmailOrPhone("<EMAIL>")
            typePassword("Azerty@123")
            submit()
        } verify {
            emailHasNoError()
            passwordHasNoError()
            // successIsDisplayed()
        }
    }

    @Test
    fun signIn_phone_password_success() {
        launchPrivateAccountSignInScreen(
            rule = composeTestRule,
            authenticationRepository = AuthenticationRepositoryConfigurableImpl()
        ) {
            typeEmailOrPhone("**********")
            typePassword("Azerty@123")
            submit()
        } verify {
            emailHasNoError()
            passwordHasNoError()
            // successIsDisplayed()
        }
    }
}