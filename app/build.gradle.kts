import com.android.build.gradle.internal.api.ApkVariantOutputImpl

plugins {
    id("avito.android.application.compose")
    id("avito.android.hilt")
    alias(libs.plugins.google.devtools.ksp)
    alias(libs.plugins.firebase.crashlytics)
    alias(libs.plugins.firebase.perf)
    alias(libs.plugins.kotlin.parcelize)
    alias(libs.plugins.apollo3)
    alias(libs.plugins.google.services)
    alias(libs.plugins.baselineprofile)
}

android {
    namespace = "se.scmv.morocco"

    compileSdk = libs.versions.compileSdk.get().toInt()

    defaultConfig {
        applicationId = "se.scmv.morocco"
        minSdk = libs.versions.minSdk.get().toInt()
        targetSdk = libs.versions.targetSdk.get().toInt()
        versionCode = libs.versions.versionCode.get().toInt()
        versionName = libs.versions.versionName.get()

        vectorDrawables.useSupportLibrary = true
        multiDexEnabled = true
        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        resourceConfigurations += listOf("fr", "ar")
    }

    buildTypes {
        getByName("release") {
            manifestPlaceholders["crashlyticsCollectionEnabled"] = true
            buildConfigField("String", "TIKTOK_APP_ID", "\"7491252446656806920\"")
            isMinifyEnabled = true
            isShrinkResources = true
            proguardFiles(
                getDefaultProguardFile("proguard-android.txt"),
                "proguard-rules.pro"
            )
            signingConfig = signingConfigs.findByName("release")
            // Ensure Baseline Profile is fresh for release builds
            baselineProfile.automaticGenerationDuringBuild = true
        }

        getByName("debug") {
            buildConfigField("String", "TIKTOK_APP_ID", "\"7491252446656806920\"")
            manifestPlaceholders["crashlyticsCollectionEnabled"] = false
        }

        applicationVariants.all {
            outputs.all {
                val apkName = when(buildType.name){
                    "debug" -> "Avito(${defaultConfig.versionName})-debug.apk"
                    "release" -> "Avito(${defaultConfig.versionName})-release.apk"
                    else -> "Avito(${defaultConfig.versionName}).apk"
                }
                if (this is ApkVariantOutputImpl) {
                    outputFileName = apkName
                }
            }
        }

        create("benchmark") {
            initWith(buildTypes.getByName("release"))
            matchingFallbacks += listOf("release")
            isDebuggable = false
            proguardFiles("benchmark-rules.pro")
        }
    }

    signingConfigs {
        register("release") {
            storeFile = file("../keystore")
            storePassword = "SCMVdr01d"
            keyAlias = "scmvappkey"
            keyPassword = "SCMVdr01d"
        }
        lint {
            abortOnError = false
        }
    }

    buildFeatures {
        dataBinding = true
        viewBinding = true
        buildConfig = true
    }

    bundle {
        language {
            // Specifies that the app bundle should not support
            // configuration APKs for language resources. These
            // resources are instead packaged with each base and
            // dynamic feature APK.
            enableSplit = false
        }
        density {
            //some Huawei phones handle it bad
            enableSplit = false
        }
    }

    compileOptions {
        isCoreLibraryDesugaringEnabled = true
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    kotlinOptions {
        freeCompilerArgs = listOf("-Xjvm-default=all")
        jvmTarget = libs.versions.jvmTarget.get()
    }

    packaging {
        resources {
            excludes += setOf(
                "META-INF/core_release.kotlin_module",
                "META-INF/LICENSE.txt",
                "META-INF/LICENSE",
                "META-INF/NOTICE.txt",
                "META-INF/NOTICE",
                "META-INF/DEPENDENCIES",
                "**/*"
            )
        }
    }

    useLibrary("org.apache.http.legacy")

    sourceSets {
        getByName("main") {
            jniLibs.srcDirs("libs")
        }
    }
}

dependencies {
    // Core
    implementation(libs.androidx.activity.compose)
    implementation(libs.androidx.core.splashscreen)
    implementation(libs.androidx.core.ktx)

    // Apollo dependencies
    apolloMetadata(project(":core:data"))
    implementation(project(":core:data"))
    implementation(project(":core:datastore"))

    implementation(project(":core:designsystem"))
    implementation(project(":core:domain"))
    implementation(project(":core:common"))
    implementation(project(":core:ui"))
    implementation(project(":core:analytics"))
    implementation(project(":core:orion"))
    implementation(project(":feature:authentication"))
    implementation(project(":feature:shop"))
    implementation(project(":feature:info"))
    implementation(project(":feature:account"))
    implementation(project(":feature:ad"))
    implementation(project(":orion-lib"))
    implementation(project(":core:adstickybanner"))

    // Desugar
    coreLibraryDesugaring(libs.desugar.jdk.libs)


    // Presentation
    implementation(libs.androidx.navigation)
    implementation(libs.androidx.hilt.navigation.compose)
    implementation(libs.androidx.multidex)
    implementation(libs.androidx.legacy.support)
    implementation(libs.androidx.appcompat)
    implementation(libs.androidx.appcompat.resources)
    implementation(libs.androidx.fragment)
    implementation(libs.com.google.material)
    implementation(libs.com.github.glide)
    ksp(libs.com.github.glide.compiler)
    implementation(libs.com.github.kizitonwose)

    // Firebase
    implementation(platform(libs.com.google.firebase.bom))
    implementation(libs.com.google.firebase.core)
    implementation(libs.com.google.firebase.invites)
    implementation(libs.com.google.firebase.messaging)
    implementation(libs.com.google.firebase.config)
    implementation(libs.com.google.firebase.analytics)
    implementation(libs.com.google.firebase.crashlytics)
    implementation(libs.com.google.firebase.perf)

    // Google services
    implementation(libs.com.google.android.gms.base)
    implementation(libs.com.google.android.gms.ads)
    implementation(libs.com.google.android.gms.auth)
    implementation(libs.com.google.android.gms.location)
    implementation(libs.com.google.android.gms.analytics)
    implementation(libs.com.google.android.gms.places)
    implementation(libs.com.google.android.gms.maps)
    implementation(libs.com.google.android.play.review)
    implementation(libs.com.google.android.play.update)
    implementation(libs.com.google.android.exoplayer)
    implementation(libs.com.google.ads.mediation)
    implementation(libs.com.google.android.billing)

    //Apollo
    implementation(libs.apollo3.runtime)
    implementation(libs.apollo3.normalized.cache)

    // Facebook
    implementation(libs.com.facebook.sdk)
    implementation(libs.com.facebook.audience)
    implementation(libs.com.facebook.annotation)

    // Local
    implementation(libs.androidx.preference)
    implementation(libs.androidx.work)

    implementation(libs.appBoy)
    implementation(libs.easypermissions)

    //clarity
    implementation(libs.microsoft.clarity)

    //... other dependencies
    implementation(libs.tiktok.business)
    //to listen for app life cycle
    implementation (libs.androidx.lifecycle.process)
    implementation (libs.androidx.lifecycle.common.java8)
}

baselineProfile {
    // Don't build on every iteration of a full assemble.
    // Instead enable generation directly for the release build variant.
    automaticGenerationDuringBuild = false
}

apollo {
    service("avito") {
        packageName.set("se.scmv.morocco")
    }
}

// Ensure datastore protobuf classes are generated before app's KSP
tasks.matching { it.name.contains("ksp") }.configureEach {
    dependsOn(":core:datastore:compileDebugKotlin")
    dependsOn(":core:datastore:generateProtos")
}